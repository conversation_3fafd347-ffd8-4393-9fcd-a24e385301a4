'use client'

import Link from 'next/link'
import Image from 'next/image'

interface SidebarHeaderProps {
  className?: string
}

export function SidebarHeader({ className = '' }: SidebarHeaderProps) {
  return (
    <div className={`flex min-h-16 items-center justify-center px-4 ${className}`}>
      <Link 
        href="/" 
        className="flex items-center justify-center transition-opacity hover:opacity-80 focus:opacity-80 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-base-100 rounded-lg"
        aria-label="Go to homepage"
      >
        <Image 
          src="/logo-light.svg" 
          alt="Company logo" 
          width={103} 
          height={20}
          priority
        />
      </Link>
    </div>
  )
}
