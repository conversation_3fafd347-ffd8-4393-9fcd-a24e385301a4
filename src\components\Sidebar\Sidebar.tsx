'use client'

import { useEffect, useRef } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { useSidebarStore, type NavigationItem, type FooterItem } from '@/stores'

interface SidebarProps {
  className?: string
}

interface SidebarHeaderProps {
  className?: string
}

interface SidebarNavigationProps {
  className?: string
}

interface SidebarFooterProps {
  className?: string
}

interface NavigationItemComponentProps {
  item: NavigationItem
  isActive: boolean
  onItemClick: () => void
}

interface FooterItemComponentProps {
  item: FooterItem
  onItemClick: () => void
}

// Internal Header Component
function SidebarHeader({ className = '' }: SidebarHeaderProps) {
  return (
    <div className={`flex min-h-16 items-center justify-center px-4 ${className}`}>
      <Link
        href="/"
        className="flex items-center justify-center transition-opacity hover:opacity-80 focus:opacity-80 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-base-100 rounded-lg"
        aria-label="Go to homepage"
      >
        <Image src="/logo-light.svg" alt="Company logo" width={103} height={20} priority />
      </Link>
    </div>
  )
}

// Internal Navigation Item Component
function NavigationItemComponent({ item, isActive, onItemClick }: NavigationItemComponentProps) {
  return (
    <li>
      <Link
        href={item.href}
        onClick={onItemClick}
        className={`
          flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200
          hover:bg-base-200 focus:bg-base-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-base-100
          ${
            isActive
              ? 'bg-primary text-primary-content hover:bg-primary/90 focus:bg-primary/90'
              : 'text-base-content hover:text-base-content focus:text-base-content'
          }
        `}
        aria-current={isActive ? 'page' : undefined}
      >
        <span className={`${item.icon} text-lg flex-shrink-0`} aria-hidden="true" />
        <span className="font-medium truncate">{item.label}</span>
        {item.badge && (
          <span
            className={`
              ml-auto px-2 py-1 text-xs font-semibold rounded-full flex-shrink-0
              ${
                isActive
                  ? 'bg-primary-content/20 text-primary-content'
                  : 'bg-primary text-primary-content'
              }
            `}
            aria-label={`${item.label} has ${item.badge} ${
              typeof item.badge === 'number' ? 'items' : ''
            }`}
          >
            {item.badge}
          </span>
        )}
      </Link>
    </li>
  )
}

// Internal Navigation Component
function SidebarNavigation({ className = '' }: SidebarNavigationProps) {
  const pathname = usePathname()
  const { navigationItems, isMobile, setIsOpen } = useSidebarStore()

  const handleItemClick = () => {
    if (isMobile) {
      setIsOpen(false)
    }
  }

  return (
    <nav
      className={`flex-1 min-h-0 overflow-y-auto px-3 py-4 ${className}`}
      aria-label="Main navigation"
    >
      <ul className="space-y-1" role="list">
        {navigationItems.map((item) => (
          <NavigationItemComponent
            key={item.id}
            item={item}
            isActive={pathname === item.href}
            onItemClick={handleItemClick}
          />
        ))}
      </ul>
    </nav>
  )
}

// Internal Footer Item Component
function FooterItemComponent({ item, onItemClick }: FooterItemComponentProps) {
  return (
    <li>
      <Link
        href={item.href}
        onClick={onItemClick}
        className="
          flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200
          text-base-content/70 hover:text-base-content hover:bg-base-200
          focus:text-base-content focus:bg-base-200 focus:outline-none
          focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-base-100
        "
      >
        <span className={`${item.icon} text-lg flex-shrink-0`} aria-hidden="true" />
        <span className="font-medium truncate">{item.label}</span>
      </Link>
    </li>
  )
}

// Internal Footer Component
function SidebarFooter({ className = '' }: SidebarFooterProps) {
  const { footerItems, isMobile, setIsOpen } = useSidebarStore()

  const handleItemClick = () => {
    if (isMobile) {
      setIsOpen(false)
    }
  }

  return (
    <div className={`px-3 py-4 border-t border-base-300 ${className}`}>
      <nav aria-label="Secondary navigation">
        <ul className="space-y-1" role="list">
          {footerItems.map((item) => (
            <FooterItemComponent key={item.id} item={item} onItemClick={handleItemClick} />
          ))}
        </ul>
      </nav>
    </div>
  )
}

export function Sidebar({ className = '' }: SidebarProps) {
  const { isOpen, isMobile, setIsOpen, setIsMobile } = useSidebarStore()
  const sidebarRef = useRef<HTMLDivElement>(null)

  // Handle mobile detection and window resize
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 1024 // lg breakpoint
      setIsMobile(mobile)

      // Auto-close sidebar on mobile when switching from desktop
      if (mobile && isOpen) {
        setIsOpen(false)
      }
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [setIsMobile, isOpen, setIsOpen])

  // Handle escape key to close sidebar
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false)
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen, setIsOpen])

  // Handle click outside to close sidebar on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isMobile &&
        isOpen &&
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    if (isMobile && isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isMobile, isOpen, setIsOpen])

  // Prevent body scroll when sidebar is open on mobile
  useEffect(() => {
    if (isMobile && isOpen) {
      document.body.style.overflow = 'hidden'
      return () => {
        document.body.style.overflow = 'unset'
      }
    }
  }, [isMobile, isOpen])

  return (
    <>
      {/* Backdrop overlay for mobile */}
      {isMobile && isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden transition-opacity duration-300"
          onClick={() => setIsOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <aside
        ref={sidebarRef}
        className={`
          fixed top-4 bottom-4 left-4 z-50
          w-72 bg-[var(--layout-sidebar-background)] rounded-box
          flex flex-col shadow-xl border border-base-300
          transition-transform duration-300 ease-in-out
          ${
            isMobile
              ? isOpen
                ? 'translate-x-0'
                : '-translate-x-full'
              : isOpen
              ? 'translate-x-0'
              : '-translate-x-full lg:translate-x-0'
          }
          ${className}
        `}
        aria-label="Main sidebar"
        aria-hidden={isMobile ? !isOpen : false}
      >
        {/* Header Section - Fixed */}
        <SidebarHeader />

        {/* Navigation Section - Scrollable */}
        <SidebarNavigation />

        {/* Footer Section - Fixed */}
        <SidebarFooter />
      </aside>
    </>
  )
}
