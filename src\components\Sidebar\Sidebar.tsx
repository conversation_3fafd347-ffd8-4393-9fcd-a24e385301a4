'use client'

import { useEffect, useRef } from 'react'
import { useSidebarStore } from '@/stores'
import { SidebarHeader } from './SidebarHeader'
import { SidebarNavigation } from './SidebarNavigation'
import { SidebarFooter } from './SidebarFooter'

interface SidebarProps {
  className?: string
}

export function Sidebar({ className = '' }: SidebarProps) {
  const { isOpen, isMobile, setIsOpen, setIsMobile } = useSidebarStore()
  const sidebarRef = useRef<HTMLDivElement>(null)

  // Handle mobile detection and window resize
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 1024 // lg breakpoint
      setIsMobile(mobile)
      
      // Auto-close sidebar on mobile when switching from desktop
      if (mobile && isOpen) {
        setIsOpen(false)
      }
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [setIsMobile, isOpen, setIsOpen])

  // Handle escape key to close sidebar
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false)
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen, setIsOpen])

  // Handle click outside to close sidebar on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isMobile &&
        isOpen &&
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    if (isMobile && isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isMobile, isOpen, setIsOpen])

  // Prevent body scroll when sidebar is open on mobile
  useEffect(() => {
    if (isMobile && isOpen) {
      document.body.style.overflow = 'hidden'
      return () => {
        document.body.style.overflow = 'unset'
      }
    }
  }, [isMobile, isOpen])

  return (
    <>
      {/* Backdrop overlay for mobile */}
      {isMobile && isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden transition-opacity duration-300"
          onClick={() => setIsOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <aside
        ref={sidebarRef}
        className={`
          fixed top-4 bottom-4 left-4 z-50
          w-72 bg-[var(--layout-sidebar-background)] rounded-box
          flex flex-col shadow-xl border border-base-300
          transition-transform duration-300 ease-in-out
          ${isMobile 
            ? (isOpen ? 'translate-x-0' : '-translate-x-full')
            : (isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0')
          }
          ${className}
        `}
        aria-label="Main sidebar"
        aria-hidden={isMobile ? !isOpen : false}
      >
        {/* Header Section - Fixed */}
        <SidebarHeader />

        {/* Navigation Section - Scrollable */}
        <SidebarNavigation />

        {/* Footer Section - Fixed */}
        <SidebarFooter />
      </aside>
    </>
  )
}
