'use client'

import Link from 'next/link'
import { useSidebarStore, type FooterItem } from '@/stores'

interface SidebarFooterProps {
  className?: string
}

interface FooterItemComponentProps {
  item: FooterItem
  onItemClick: () => void
}

function FooterItemComponent({ item, onItemClick }: FooterItemComponentProps) {
  return (
    <li>
      <Link
        href={item.href}
        onClick={onItemClick}
        className="
          flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200
          text-base-content/70 hover:text-base-content hover:bg-base-200 
          focus:text-base-content focus:bg-base-200 focus:outline-none 
          focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-base-100
        "
      >
        <span 
          className={`${item.icon} text-lg flex-shrink-0`} 
          aria-hidden="true"
        />
        <span className="font-medium truncate">{item.label}</span>
      </Link>
    </li>
  )
}

export function SidebarFooter({ className = '' }: SidebarFooterProps) {
  const { footerItems, isMobile, setIsOpen } = useSidebarStore()

  const handleItemClick = () => {
    if (isMobile) {
      setIsOpen(false)
    }
  }

  return (
    <div className={`px-3 py-4 border-t border-base-300 ${className}`}>
      <nav aria-label="Secondary navigation">
        <ul className="space-y-1" role="list">
          {footerItems.map((item) => (
            <FooterItemComponent
              key={item.id}
              item={item}
              onItemClick={handleItemClick}
            />
          ))}
        </ul>
      </nav>
    </div>
  )
}
