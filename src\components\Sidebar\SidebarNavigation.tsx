'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useSidebarStore, type NavigationItem } from '@/stores'

interface SidebarNavigationProps {
  className?: string
}

interface NavigationItemComponentProps {
  item: NavigationItem
  isActive: boolean
  onItemClick: () => void
}

function NavigationItemComponent({ item, isActive, onItemClick }: NavigationItemComponentProps) {
  return (
    <li>
      <Link
        href={item.href}
        onClick={onItemClick}
        className={`
          flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200
          hover:bg-base-200 focus:bg-base-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-base-100
          ${isActive 
            ? 'bg-primary text-primary-content hover:bg-primary/90 focus:bg-primary/90' 
            : 'text-base-content hover:text-base-content focus:text-base-content'
          }
        `}
        aria-current={isActive ? 'page' : undefined}
      >
        <span 
          className={`${item.icon} text-lg flex-shrink-0`} 
          aria-hidden="true"
        />
        <span className="font-medium truncate">{item.label}</span>
        {item.badge && (
          <span 
            className={`
              ml-auto px-2 py-1 text-xs font-semibold rounded-full flex-shrink-0
              ${isActive 
                ? 'bg-primary-content/20 text-primary-content' 
                : 'bg-primary text-primary-content'
              }
            `}
            aria-label={`${item.label} has ${item.badge} ${typeof item.badge === 'number' ? 'items' : ''}`}
          >
            {item.badge}
          </span>
        )}
      </Link>
    </li>
  )
}

export function SidebarNavigation({ className = '' }: SidebarNavigationProps) {
  const pathname = usePathname()
  const { navigationItems, isMobile, setIsOpen } = useSidebarStore()

  const handleItemClick = () => {
    if (isMobile) {
      setIsOpen(false)
    }
  }

  return (
    <nav 
      className={`flex-1 min-h-0 overflow-y-auto px-3 py-4 ${className}`}
      aria-label="Main navigation"
    >
      <ul className="space-y-1" role="list">
        {navigationItems.map((item) => (
          <NavigationItemComponent
            key={item.id}
            item={item}
            isActive={pathname === item.href}
            onItemClick={handleItemClick}
          />
        ))}
      </ul>
    </nav>
  )
}
